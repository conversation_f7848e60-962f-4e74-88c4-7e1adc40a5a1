******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Jun 27 00:50:59 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000008f1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000a68  0001f598  R  X
  SRAM                  20200000   00008000  0000022d  00007dd3  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000a68   00000a68    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000948   00000948    r-x .text
  00000a08    00000a08    00000008   00000008    r-- .rodata
  00000a10    00000a10    00000058   00000058    r-- .cinit
20200000    20200000    0000002d   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .data
  20200028    20200028    00000005   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000948     
                  000000c0    00000394     oled_hardware_i2c.o (.text.OLED_Init)
                  00000454    00000134     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00000588    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000622    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000624    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000006a0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000006fe    00000002     oled_app.o (.text.oled_task)
                  00000700    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00000754    00000044     scheduler.o (.text.scheduler_run)
                  00000798    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000007d8    00000040     clock.o (.text.SysTick_Init)
                  00000818    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000854    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000888    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000008bc    00000034     clock.o (.text.mspm0_delay_ms)
                  000008f0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000918    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000093e    0000001a     empty.o (.text.main)
                  00000958    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00000970    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000986    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000099a    00000002     --HOLE-- [fill = 0]
                  0000099c    00000014     scheduler.o (.text.scheduler_init)
                  000009b0    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000009c2    00000002     --HOLE-- [fill = 0]
                  000009c4    00000010     interrupt.o (.text.SysTick_Handler)
                  000009d4    0000000c     empty.o (.text.led_blink_task)
                  000009e0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000009ea    00000002     --HOLE-- [fill = 0]
                  000009ec    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000009f4    00000006     libc.a : exit.c.obj (.text:abort)
                  000009fa    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000009fe    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000a02    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000a06    00000002     --HOLE-- [fill = 0]

.cinit     0    00000a10    00000058     
                  00000a10    00000030     (.cinit..data.load) [load image]
                  00000a40    0000000c     (__TI_handler_table)
                  00000a4c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000a54    00000010     (__TI_cinit_table)
                  00000a64    00000004     --HOLE-- [fill = 0]

.rodata    0    00000a08    00000008     
                  00000a08    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00000a0a    00000006     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000028     UNINITIALIZED
                  20200000    00000024     scheduler.o (.data.scheduler_task)
                  20200024    00000004     clock.o (.data.sys_tick)

.bss       0    20200028    00000005     UNINITIALIZED
                  20200028    00000004     (.common:start_time)
                  2020002c    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             272    2         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       scheduler.o                    88     0         37     
       empty.o                        38     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         404    194       37     
                                                              
    .\APP\
       oled_app.o                     2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         2      0         0      
                                                              
    .\BSP\OLED\
       oled_hardware_i2c.o            1224   0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1224   0         0      
                                                              
    .\System\
       clock.o                        140    0         8      
       interrupt.o                    16     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         156    0         8      
                                                              
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       132    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         142    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      84        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   2368   278       557    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000a54 records: 2, size/record: 8, table size: 16
	.data: load addr=00000a10, load size=00000030 bytes, run addr=20200000, run size=00000028 bytes, compression=copy
	.bss: load addr=00000a4c, load size=00000008 bytes, run addr=20200028, run size=00000005 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000a40 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000623  ADC0_IRQHandler               
00000623  ADC1_IRQHandler               
00000623  AES_IRQHandler                
000009fa  C$$EXIT                       
00000623  CANFD0_IRQHandler             
00000623  DAC0_IRQHandler               
000009e1  DL_Common_delayCycles         
000006a1  DL_I2C_fillControllerTXFIFO   
00000919  DL_I2C_setClockConfig         
00000623  DMA_IRQHandler                
00000623  Default_Handler               
00000623  GROUP0_IRQHandler             
00000623  GROUP1_IRQHandler             
000009fb  HOSTexit                      
00000623  HardFault_Handler             
00000623  I2C0_IRQHandler               
00000623  I2C1_IRQHandler               
00000623  NMI_Handler                   
000000c1  OLED_Init                     
00000455  OLED_WR_Byte                  
00000623  PendSV_Handler                
00000623  RTC_IRQHandler                
000009ff  Reset_Handler                 
00000623  SPI0_IRQHandler               
00000623  SPI1_IRQHandler               
00000623  SVC_Handler                   
00000855  SYSCFG_DL_GPIO_init           
00000701  SYSCFG_DL_I2C_OLED_init       
00000799  SYSCFG_DL_SYSCTL_init         
00000987  SYSCFG_DL_init                
00000889  SYSCFG_DL_initPower           
000009c5  SysTick_Handler               
000007d9  SysTick_Init                  
00000623  TIMA0_IRQHandler              
00000623  TIMA1_IRQHandler              
00000623  TIMG0_IRQHandler              
00000623  TIMG12_IRQHandler             
00000623  TIMG6_IRQHandler              
00000623  TIMG7_IRQHandler              
00000623  TIMG8_IRQHandler              
00000623  UART0_IRQHandler              
00000623  UART1_IRQHandler              
00000623  UART2_IRQHandler              
00000623  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000a54  __TI_CINIT_Base               
00000a64  __TI_CINIT_Limit              
00000a64  __TI_CINIT_Warm               
00000a40  __TI_Handler_Table_Base       
00000a4c  __TI_Handler_Table_Limit      
00000819  __TI_auto_init_nobinit_nopinit
00000625  __TI_decompress_lzss          
000009b1  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000971  __TI_zero_init_nomemset       
000009ed  __aeabi_memcpy                
000009ed  __aeabi_memcpy4               
000009ed  __aeabi_memcpy8               
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
000008f1  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00000a03  _system_pre_init              
000009f5  abort                         
ffffffff  binit                         
00000000  interruptVectors              
000009d5  led_blink_task                
0000093f  main                          
00000589  memcpy                        
UNDEFED   mpu6050_task                  
000008bd  mspm0_delay_ms                
00000959  mspm0_get_clock_ms            
000006ff  oled_task                     
0000099d  scheduler_init                
00000755  scheduler_run                 
20200028  start_time                    
20200024  sys_tick                      
2020002c  task_num                      


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  OLED_Init                     
00000200  __STACK_SIZE                  
00000455  OLED_WR_Byte                  
00000589  memcpy                        
00000623  ADC0_IRQHandler               
00000623  ADC1_IRQHandler               
00000623  AES_IRQHandler                
00000623  CANFD0_IRQHandler             
00000623  DAC0_IRQHandler               
00000623  DMA_IRQHandler                
00000623  Default_Handler               
00000623  GROUP0_IRQHandler             
00000623  GROUP1_IRQHandler             
00000623  HardFault_Handler             
00000623  I2C0_IRQHandler               
00000623  I2C1_IRQHandler               
00000623  NMI_Handler                   
00000623  PendSV_Handler                
00000623  RTC_IRQHandler                
00000623  SPI0_IRQHandler               
00000623  SPI1_IRQHandler               
00000623  SVC_Handler                   
00000623  TIMA0_IRQHandler              
00000623  TIMA1_IRQHandler              
00000623  TIMG0_IRQHandler              
00000623  TIMG12_IRQHandler             
00000623  TIMG6_IRQHandler              
00000623  TIMG7_IRQHandler              
00000623  TIMG8_IRQHandler              
00000623  UART0_IRQHandler              
00000623  UART1_IRQHandler              
00000623  UART2_IRQHandler              
00000623  UART3_IRQHandler              
00000625  __TI_decompress_lzss          
000006a1  DL_I2C_fillControllerTXFIFO   
000006ff  oled_task                     
00000701  SYSCFG_DL_I2C_OLED_init       
00000755  scheduler_run                 
00000799  SYSCFG_DL_SYSCTL_init         
000007d9  SysTick_Init                  
00000819  __TI_auto_init_nobinit_nopinit
00000855  SYSCFG_DL_GPIO_init           
00000889  SYSCFG_DL_initPower           
000008bd  mspm0_delay_ms                
000008f1  _c_int00_noargs               
00000919  DL_I2C_setClockConfig         
0000093f  main                          
00000959  mspm0_get_clock_ms            
00000971  __TI_zero_init_nomemset       
00000987  SYSCFG_DL_init                
0000099d  scheduler_init                
000009b1  __TI_decompress_none          
000009c5  SysTick_Handler               
000009d5  led_blink_task                
000009e1  DL_Common_delayCycles         
000009ed  __aeabi_memcpy                
000009ed  __aeabi_memcpy4               
000009ed  __aeabi_memcpy8               
000009f5  abort                         
000009fa  C$$EXIT                       
000009fb  HOSTexit                      
000009ff  Reset_Handler                 
00000a03  _system_pre_init              
00000a40  __TI_Handler_Table_Base       
00000a4c  __TI_Handler_Table_Limit      
00000a54  __TI_CINIT_Base               
00000a64  __TI_CINIT_Limit              
00000a64  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200024  sys_tick                      
20200028  start_time                    
2020002c  task_num                      
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            
UNDEFED   mpu6050_task                  

[96 symbols]
