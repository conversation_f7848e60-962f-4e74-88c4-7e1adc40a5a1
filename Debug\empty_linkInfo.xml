<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty.out -mempty.map -iD:/TI/CCS/mspm0_sdk_2_05_00_05/source -iD:/Projects/TI/empty -iD:/Projects/TI/empty/Debug/syscfg -iD:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./scheduler.o ./APP/oled_app.o ./BSP/OLED/oled_hardware_i2c.o ./System/clock.o ./System/interrupt.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x685d7a73</link_time>
   <link_errors>0x1</link_errors>
   <output_file>D:\Projects\TI\empty\Debug\empty.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x8f1</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>scheduler.o</file>
         <name>scheduler.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\Projects\TI\empty\Debug\.\APP\</path>
         <kind>object</kind>
         <file>oled_app.o</file>
         <name>oled_app.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\Projects\TI\empty\Debug\.\BSP\OLED\</path>
         <kind>object</kind>
         <file>oled_hardware_i2c.o</file>
         <name>oled_hardware_i2c.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>D:\Projects\TI\empty\Debug\.\System\</path>
         <kind>object</kind>
         <file>clock.o</file>
         <name>clock.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>D:\Projects\TI\empty\Debug\.\System\</path>
         <kind>object</kind>
         <file>interrupt.o</file>
         <name>interrupt.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\Projects\TI\empty\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text.OLED_Init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x394</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text.OLED_WR_Byte</name>
         <load_address>0x454</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x454</run_address>
         <size>0x134</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text:memcpy</name>
         <load_address>0x588</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x588</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x622</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x622</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x624</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x624</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.text.DL_I2C_fillControllerTXFIFO</name>
         <load_address>0x6a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a0</run_address>
         <size>0x5e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.oled_task</name>
         <load_address>0x6fe</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6fe</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0x700</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x700</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-99">
         <name>.text.scheduler_run</name>
         <load_address>0x754</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x754</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x798</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x798</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.text.SysTick_Init</name>
         <load_address>0x7d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x818</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x818</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-be">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x854</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x854</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x888</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x888</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.mspm0_delay_ms</name>
         <load_address>0x8bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8bc</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-53">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x8f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8f0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0x918</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x918</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.text.main</name>
         <load_address>0x93e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x93e</run_address>
         <size>0x1a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.mspm0_get_clock_ms</name>
         <load_address>0x958</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x958</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x970</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x970</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x986</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x986</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text.scheduler_init</name>
         <load_address>0x99c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x99c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x9b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9b0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x9c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9c4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text.led_blink_task</name>
         <load_address>0x9d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9d4</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x9e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9e0</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-43">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x9ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9ec</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.text:abort</name>
         <load_address>0x9f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9f4</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.HOSTexit</name>
         <load_address>0x9fa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9fa</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x9fe</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9fe</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-67">
         <name>.text._system_pre_init</name>
         <load_address>0xa02</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa02</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-145">
         <name>.cinit..data.load</name>
         <load_address>0xa10</load_address>
         <readonly>true</readonly>
         <run_address>0xa10</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-143">
         <name>__TI_handler_table</name>
         <load_address>0xa40</load_address>
         <readonly>true</readonly>
         <run_address>0xa40</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-146">
         <name>.cinit..bss.load</name>
         <load_address>0xa4c</load_address>
         <readonly>true</readonly>
         <run_address>0xa4c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-144">
         <name>__TI_cinit_table</name>
         <load_address>0xa54</load_address>
         <readonly>true</readonly>
         <run_address>0xa54</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e9">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0xa08</load_address>
         <readonly>true</readonly>
         <run_address>0xa08</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-cd">
         <name>.data.scheduler_task</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.data.sys_tick</name>
         <load_address>0x20200024</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200024</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.common:task_num</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020002c</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-ea">
         <name>.common:start_time</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200028</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-148">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_abbrev</name>
         <load_address>0x10d</load_address>
         <run_address>0x10d</run_address>
         <size>0x1b8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_abbrev</name>
         <load_address>0x2c5</load_address>
         <run_address>0x2c5</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_abbrev</name>
         <load_address>0x332</load_address>
         <run_address>0x332</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_abbrev</name>
         <load_address>0x40e</load_address>
         <run_address>0x40e</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_abbrev</name>
         <load_address>0x43a</load_address>
         <run_address>0x43a</run_address>
         <size>0x2a9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_abbrev</name>
         <load_address>0x6e3</load_address>
         <run_address>0x6e3</run_address>
         <size>0x1b1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_abbrev</name>
         <load_address>0x894</load_address>
         <run_address>0x894</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_abbrev</name>
         <load_address>0x8c0</load_address>
         <run_address>0x8c0</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_abbrev</name>
         <load_address>0x922</load_address>
         <run_address>0x922</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0xb09</load_address>
         <run_address>0xb09</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_abbrev</name>
         <load_address>0xbb8</load_address>
         <run_address>0xbb8</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_abbrev</name>
         <load_address>0xd28</load_address>
         <run_address>0xd28</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_abbrev</name>
         <load_address>0xd61</load_address>
         <run_address>0xd61</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_abbrev</name>
         <load_address>0xe23</load_address>
         <run_address>0xe23</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_abbrev</name>
         <load_address>0xe93</load_address>
         <run_address>0xe93</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_abbrev</name>
         <load_address>0xf20</load_address>
         <run_address>0xf20</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_abbrev</name>
         <load_address>0xfb8</load_address>
         <run_address>0xfb8</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_abbrev</name>
         <load_address>0xfe4</load_address>
         <run_address>0xfe4</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_abbrev</name>
         <load_address>0x100b</load_address>
         <run_address>0x100b</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.debug_abbrev</name>
         <load_address>0x1030</load_address>
         <run_address>0x1030</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x759</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_info</name>
         <load_address>0x759</load_address>
         <run_address>0x759</run_address>
         <size>0x1c68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x23c1</load_address>
         <run_address>0x23c1</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_info</name>
         <load_address>0x2441</load_address>
         <run_address>0x2441</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_info</name>
         <load_address>0x2580</load_address>
         <run_address>0x2580</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_info</name>
         <load_address>0x25bb</load_address>
         <run_address>0x25bb</run_address>
         <size>0x2633</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_info</name>
         <load_address>0x4bee</load_address>
         <run_address>0x4bee</run_address>
         <size>0x4af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_info</name>
         <load_address>0x509d</load_address>
         <run_address>0x509d</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_info</name>
         <load_address>0x50d8</load_address>
         <run_address>0x50d8</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_info</name>
         <load_address>0x514d</load_address>
         <run_address>0x514d</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x5e0f</load_address>
         <run_address>0x5e0f</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_info</name>
         <load_address>0x6232</load_address>
         <run_address>0x6232</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_info</name>
         <load_address>0x6976</load_address>
         <run_address>0x6976</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_info</name>
         <load_address>0x69bc</load_address>
         <run_address>0x69bc</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0x6b4e</load_address>
         <run_address>0x6b4e</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x6c14</load_address>
         <run_address>0x6c14</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_info</name>
         <load_address>0x6d90</load_address>
         <run_address>0x6d90</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_info</name>
         <load_address>0x6e88</load_address>
         <run_address>0x6e88</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_info</name>
         <load_address>0x6ec3</load_address>
         <run_address>0x6ec3</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_info</name>
         <load_address>0x705c</load_address>
         <run_address>0x705c</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-149">
         <name>.debug_info</name>
         <load_address>0x7356</load_address>
         <run_address>0x7356</run_address>
         <size>0x8e</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_ranges</name>
         <load_address>0x18</load_address>
         <run_address>0x18</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_ranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_ranges</name>
         <load_address>0x78</load_address>
         <run_address>0x78</run_address>
         <size>0x1b0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_ranges</name>
         <load_address>0x228</load_address>
         <run_address>0x228</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_ranges</name>
         <load_address>0x258</load_address>
         <run_address>0x258</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_ranges</name>
         <load_address>0x430</load_address>
         <run_address>0x430</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_ranges</name>
         <load_address>0x478</load_address>
         <run_address>0x478</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_ranges</name>
         <load_address>0x4c0</load_address>
         <run_address>0x4c0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_ranges</name>
         <load_address>0x4d8</load_address>
         <run_address>0x4d8</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_ranges</name>
         <load_address>0x528</load_address>
         <run_address>0x528</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_ranges</name>
         <load_address>0x540</load_address>
         <run_address>0x540</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x476</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_str</name>
         <load_address>0x476</load_address>
         <run_address>0x476</run_address>
         <size>0x170a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_str</name>
         <load_address>0x1b80</load_address>
         <run_address>0x1b80</run_address>
         <size>0x149</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_str</name>
         <load_address>0x1cc9</load_address>
         <run_address>0x1cc9</run_address>
         <size>0x166</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_str</name>
         <load_address>0x1e2f</load_address>
         <run_address>0x1e2f</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_str</name>
         <load_address>0x1ede</load_address>
         <run_address>0x1ede</run_address>
         <size>0xf55</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_str</name>
         <load_address>0x2e33</load_address>
         <run_address>0x2e33</run_address>
         <size>0x4ca</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_str</name>
         <load_address>0x32fd</load_address>
         <run_address>0x32fd</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_str</name>
         <load_address>0x33b6</load_address>
         <run_address>0x33b6</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_str</name>
         <load_address>0x3523</load_address>
         <run_address>0x3523</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x3dd2</load_address>
         <run_address>0x3dd2</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_str</name>
         <load_address>0x3ff7</load_address>
         <run_address>0x3ff7</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_str</name>
         <load_address>0x4326</load_address>
         <run_address>0x4326</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_str</name>
         <load_address>0x441b</load_address>
         <run_address>0x441b</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_str</name>
         <load_address>0x45b6</load_address>
         <run_address>0x45b6</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_str</name>
         <load_address>0x471e</load_address>
         <run_address>0x471e</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_str</name>
         <load_address>0x48f3</load_address>
         <run_address>0x48f3</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_str</name>
         <load_address>0x4a3b</load_address>
         <run_address>0x4a3b</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_frame</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0xa8</load_address>
         <run_address>0xa8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_frame</name>
         <load_address>0xd8</load_address>
         <run_address>0xd8</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_frame</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_frame</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x1ec</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_frame</name>
         <load_address>0x32c</load_address>
         <run_address>0x32c</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_frame</name>
         <load_address>0x394</load_address>
         <run_address>0x394</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_frame</name>
         <load_address>0x3b4</load_address>
         <run_address>0x3b4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_frame</name>
         <load_address>0x3d4</load_address>
         <run_address>0x3d4</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_frame</name>
         <load_address>0x500</load_address>
         <run_address>0x500</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_frame</name>
         <load_address>0x590</load_address>
         <run_address>0x590</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_frame</name>
         <load_address>0x690</load_address>
         <run_address>0x690</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_frame</name>
         <load_address>0x6b0</load_address>
         <run_address>0x6b0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0x6e8</load_address>
         <run_address>0x6e8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x710</load_address>
         <run_address>0x710</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_frame</name>
         <load_address>0x740</load_address>
         <run_address>0x740</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_frame</name>
         <load_address>0x770</load_address>
         <run_address>0x770</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_line</name>
         <load_address>0x1e9</load_address>
         <run_address>0x1e9</run_address>
         <size>0x44e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x637</load_address>
         <run_address>0x637</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_line</name>
         <load_address>0x6f3</load_address>
         <run_address>0x6f3</run_address>
         <size>0x147</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_line</name>
         <load_address>0x83a</load_address>
         <run_address>0x83a</run_address>
         <size>0x42</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0x87c</load_address>
         <run_address>0x87c</run_address>
         <size>0x10d1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_line</name>
         <load_address>0x194d</load_address>
         <run_address>0x194d</run_address>
         <size>0x2cc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_line</name>
         <load_address>0x1c19</load_address>
         <run_address>0x1c19</run_address>
         <size>0x4d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_line</name>
         <load_address>0x1c66</load_address>
         <run_address>0x1c66</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_line</name>
         <load_address>0x1dde</load_address>
         <run_address>0x1dde</run_address>
         <size>0x682</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_line</name>
         <load_address>0x2460</load_address>
         <run_address>0x2460</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_line</name>
         <load_address>0x263c</load_address>
         <run_address>0x263c</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_line</name>
         <load_address>0x2b56</load_address>
         <run_address>0x2b56</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_line</name>
         <load_address>0x2b94</load_address>
         <run_address>0x2b94</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x2c92</load_address>
         <run_address>0x2c92</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x2d52</load_address>
         <run_address>0x2d52</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_line</name>
         <load_address>0x2f1a</load_address>
         <run_address>0x2f1a</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_line</name>
         <load_address>0x2f81</load_address>
         <run_address>0x2f81</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_line</name>
         <load_address>0x2fc2</load_address>
         <run_address>0x2fc2</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_line</name>
         <load_address>0x3066</load_address>
         <run_address>0x3066</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x92</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_loc</name>
         <load_address>0x92</load_address>
         <run_address>0x92</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_loc</name>
         <load_address>0xd3</load_address>
         <run_address>0xd3</run_address>
         <size>0x12a9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_loc</name>
         <load_address>0x137c</load_address>
         <run_address>0x137c</run_address>
         <size>0x83</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_loc</name>
         <load_address>0x13ff</load_address>
         <run_address>0x13ff</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_loc</name>
         <load_address>0x1412</load_address>
         <run_address>0x1412</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_loc</name>
         <load_address>0x1764</load_address>
         <run_address>0x1764</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_loc</name>
         <load_address>0x183c</load_address>
         <run_address>0x183c</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x1c60</load_address>
         <run_address>0x1c60</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_loc</name>
         <load_address>0x1dcc</load_address>
         <run_address>0x1dcc</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_loc</name>
         <load_address>0x1e3b</load_address>
         <run_address>0x1e3b</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_loc</name>
         <load_address>0x1fa2</load_address>
         <run_address>0x1fa2</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x948</size>
         <contents>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-67"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0xa10</load_address>
         <run_address>0xa10</run_address>
         <size>0x58</size>
         <contents>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-144"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xa08</load_address>
         <run_address>0xa08</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-e9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-10d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <contents>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-5a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200028</run_address>
         <size>0x5</size>
         <contents>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-ea"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-148"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-104" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-105" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-106" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-107" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-108" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-109" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10b" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-127" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x103f</size>
         <contents>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-14a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-129" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x73e4</size>
         <contents>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-149"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12b" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x568</size>
         <contents>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-83"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12d" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4b24</size>
         <contents>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-f3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12f" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x790</size>
         <contents>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-d0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-131" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3106</size>
         <contents>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-133" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1fc8</size>
         <contents>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-a3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-13d" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-81"/>
         </contents>
      </logical_group>
      <logical_group id="lg-147" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-151" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa68</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-152" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x2d</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-153" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0xa68</used_space>
         <unused_space>0x1f598</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x948</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xa08</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xa10</start_address>
               <size>0x58</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0xa68</start_address>
               <size>0x1f598</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x22d</used_space>
         <unused_space>0x7dd3</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-109"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-10b"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x28</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200028</start_address>
               <size>0x5</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020002d</start_address>
               <size>0x7dd3</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0xa10</load_address>
            <load_size>0x30</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x28</run_size>
            <compression>copy</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0xa4c</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200028</run_address>
            <run_size>0x5</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0xa54</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0xa64</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0xa64</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0xa40</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0xa4c</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3e">
         <name>led_blink_task</name>
         <value>0x9d5</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-3f">
         <name>main</name>
         <value>0x93f</value>
         <object_component_ref idref="oc-6b"/>
      </symbol>
      <symbol id="sm-57">
         <name>SYSCFG_DL_init</name>
         <value>0x987</value>
         <object_component_ref idref="oc-89"/>
      </symbol>
      <symbol id="sm-58">
         <name>SYSCFG_DL_initPower</name>
         <value>0x889</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-59">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x855</value>
         <object_component_ref idref="oc-be"/>
      </symbol>
      <symbol id="sm-5a">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x799</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-5b">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0x701</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-66">
         <name>Default_Handler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-67">
         <name>Reset_Handler</name>
         <value>0x9ff</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-68">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-69">
         <name>NMI_Handler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6a">
         <name>HardFault_Handler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6b">
         <name>SVC_Handler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6c">
         <name>PendSV_Handler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6d">
         <name>GROUP0_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6e">
         <name>GROUP1_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6f">
         <name>TIMG8_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-70">
         <name>UART3_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-71">
         <name>ADC0_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-72">
         <name>ADC1_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>CANFD0_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>DAC0_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>SPI0_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>SPI1_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>UART1_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>UART2_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>UART0_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>TIMG0_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>TIMG6_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>TIMA0_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>TIMA1_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>TIMG7_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>TIMG12_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>I2C0_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>I2C1_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>AES_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>RTC_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>DMA_IRQHandler</name>
         <value>0x623</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-94">
         <name>scheduler_init</name>
         <value>0x99d</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-95">
         <name>task_num</name>
         <value>0x2020002c</value>
      </symbol>
      <symbol id="sm-96">
         <name>scheduler_run</name>
         <value>0x755</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-9e">
         <name>oled_task</name>
         <value>0x6ff</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-ac">
         <name>OLED_WR_Byte</name>
         <value>0x455</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-ad">
         <name>OLED_Init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-be">
         <name>mspm0_delay_ms</name>
         <value>0x8bd</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-bf">
         <name>sys_tick</name>
         <value>0x20200024</value>
         <object_component_ref idref="oc-5a"/>
      </symbol>
      <symbol id="sm-c0">
         <name>start_time</name>
         <value>0x20200028</value>
      </symbol>
      <symbol id="sm-c1">
         <name>mspm0_get_clock_ms</name>
         <value>0x959</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-c2">
         <name>SysTick_Init</name>
         <value>0x7d9</value>
         <object_component_ref idref="oc-c4"/>
      </symbol>
      <symbol id="sm-cb">
         <name>SysTick_Handler</name>
         <value>0x9c5</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-cc">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-cd">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ce">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-cf">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-d0">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-d1">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-d2">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-d3">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-d4">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-dd">
         <name>DL_Common_delayCycles</name>
         <value>0x9e1</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-e9">
         <name>DL_I2C_setClockConfig</name>
         <value>0x919</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-ea">
         <name>DL_I2C_fillControllerTXFIFO</name>
         <value>0x6a1</value>
         <object_component_ref idref="oc-ec"/>
      </symbol>
      <symbol id="sm-f5">
         <name>_c_int00_noargs</name>
         <value>0x8f1</value>
         <object_component_ref idref="oc-53"/>
      </symbol>
      <symbol id="sm-f6">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-102">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x819</value>
         <object_component_ref idref="oc-a4"/>
      </symbol>
      <symbol id="sm-10a">
         <name>_system_pre_init</name>
         <value>0xa03</value>
         <object_component_ref idref="oc-67"/>
      </symbol>
      <symbol id="sm-115">
         <name>__TI_zero_init_nomemset</name>
         <value>0x971</value>
         <object_component_ref idref="oc-4a"/>
      </symbol>
      <symbol id="sm-11e">
         <name>__TI_decompress_none</name>
         <value>0x9b1</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-129">
         <name>__TI_decompress_lzss</name>
         <value>0x625</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-133">
         <name>abort</name>
         <value>0x9f5</value>
         <object_component_ref idref="oc-9d"/>
      </symbol>
      <symbol id="sm-13d">
         <name>HOSTexit</name>
         <value>0x9fb</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-13e">
         <name>C$$EXIT</name>
         <value>0x9fa</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-144">
         <name>__aeabi_memcpy</name>
         <value>0x9ed</value>
         <object_component_ref idref="oc-43"/>
      </symbol>
      <symbol id="sm-145">
         <name>__aeabi_memcpy4</name>
         <value>0x9ed</value>
         <object_component_ref idref="oc-43"/>
      </symbol>
      <symbol id="sm-146">
         <name>__aeabi_memcpy8</name>
         <value>0x9ed</value>
         <object_component_ref idref="oc-43"/>
      </symbol>
      <symbol id="sm-160">
         <name>memcpy</name>
         <value>0x589</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-161">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-165">
         <name>mpu6050_task</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-166">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-167">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link errors: red sections failed placement</title>
</link_info>
